import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { meiti_chaxun } from '../../gongju/shebeishiPei_gongju.js';

// 更新栏容器
const Gengxinlanrongqi = styled.div`
  width: 480px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}10)`
    : props.theme.yanse.biaomian};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}40`
    : props.theme.yanse.biankuang};
  border-radius: 15px;
  overflow: hidden;
  margin-top: 8px;
  margin-bottom: 3px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 4px 12px ${props.theme.yanse.danjinse}20, ${props.theme.yinying.xiao}`
    : props.theme.yinying.xiao};

  ${meiti_chaxun.shouji} {
    width: 100%;
    max-width: none;
    border-radius: 12px;
  }

  ${meiti_chaxun.pingban} {
    width: 100%;
    max-width: none;
    border-radius: 12px;
  }
`;

// 标签栏容器
const Biaoqianlanrongqi = styled.div`
  display: flex;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}08`
    : 'rgba(0, 0, 0, 0.03)'};
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}30`
    : 'rgba(0, 0, 0, 0.1)'};
`;

// 标签项
const Biaoqianxiang = styled.button.withConfig({
  shouldForwardProp: (prop) => prop !== 'huoyue'
})`
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: ${props => props.huoyue 
    ? props.theme.yanse.wenzi_zhuyao
    : props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.huoyue 
    ? props.theme.ziti.zhongliang.zhongdeng
    : props.theme.ziti.zhongliang.putong};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  position: relative;
  
  &:hover {
    color: ${props => props.theme.mingcheng === 'anhei'
      ? props.theme.yanse.danjinse_hou
      : props.theme.yanse.wenzi_zhuyao};
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}15`
      : 'rgba(0, 0, 0, 0.05)'};
  }

  ${props => props.huoyue && `
    color: ${props.theme.mingcheng === 'anhei'
      ? props.theme.yanse.danjinse
      : props.theme.yanse.wenzi_zhuyao};
    background: ${props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}20`
      : 'rgba(0, 0, 0, 0.08)'};
  `}

  ${meiti_chaxun.shouji} {
    padding: 10px 12px;
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }

  ${meiti_chaxun.pingban} {
    padding: 10px 12px;
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }
`;

// 内容区域
const Neirongquyu = styled.div`
  padding: 16px;
  height: calc(100vh - 80px - 270px - 60px - 10px);
  overflow-y: auto;

  /* 隐藏滚动条但保留滚动功能 */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Firefox */
  scrollbar-width: none;



  ${meiti_chaxun.shouji} {
    padding: 12px;
    max-height: 400px;
    min-height: 300px;
    overflow-y: auto;

    /* 手机端自定义滚动条 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: ${props => props.theme.mingcheng === 'anhei'
        ? `linear-gradient(to bottom, ${props.theme.yanse.danjinse}05, ${props.theme.yanse.danjinse}10)`
        : 'linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(0, 0, 0, 0.03))'};
      border-radius: 4px;
      margin: 1px 0;
    }

    &::-webkit-scrollbar-thumb {
      background: ${props => props.theme.mingcheng === 'anhei'
        ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}50, ${props.theme.yanse.danjinse}70)`
        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
      border-radius: 4px;
      transition: all 0.2s ease;

      &:active {
        background: ${props => props.theme.mingcheng === 'anhei'
          ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}60, ${props.theme.yanse.danjinse}80)`
          : 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'};
      }
    }

    scrollbar-width: thin;
    scrollbar-color: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}60 ${props.theme.yanse.danjinse}10`
      : '#667eea rgba(255, 255, 255, 0.5)'};
  }

  ${meiti_chaxun.pingban} {
    padding: 12px;
    max-height: 450px;
    min-height: 350px;
    overflow-y: auto;

    /* 平板端自定义滚动条 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: ${props => props.theme.mingcheng === 'anhei'
        ? `${props.theme.yanse.danjinse}08`
        : 'rgba(0, 0, 0, 0.03)'};
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: ${props => props.theme.mingcheng === 'anhei'
        ? `${props.theme.yanse.danjinse}40`
        : 'rgba(0, 0, 0, 0.15)'};
      border-radius: 2px;
    }

    scrollbar-width: thin;
    scrollbar-color: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}40 ${props.theme.yanse.danjinse}08`
      : 'rgba(0, 0, 0, 0.15) rgba(0, 0, 0, 0.03)'};
  }
`;

// 更新项
const Gengxinxiang = styled(motion.div)`
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}15`
    : 'rgba(0, 0, 0, 0.05)'};

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}08`
      : 'rgba(0, 0, 0, 0.02)'};
    border-radius: 8px;
    margin: 0 -8px;
    padding: 12px 8px;
  }
`;

// 更新图标
const Gengxintubiao = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: white;
  background: ${props => {
    switch (props.leixing) {
      case 'gonggao': return '#10b981'; // 绿色
      case 'huodong': return '#f59e0b'; // 橙色
      case 'xinwen': return '#3b82f6'; // 蓝色
      case 'xitong': return '#8b5cf6'; // 紫色
      case 'jixie': return '#ef4444'; // 红色
      default: return '#6b7280'; // 灰色
    }
  }};

  ${meiti_chaxun.shouji} {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-right: 8px;
  }

  ${meiti_chaxun.pingban} {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-right: 8px;
  }
`;

// 更新内容
const Gengxinneirong = styled.div`
  flex: 1;
  min-width: 0;
`;

// 更新标题
const Gengxinbiaoti = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin-bottom: 4px;
  line-height: 1.4;
  
  /* 文本溢出处理 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  ${meiti_chaxun.shouji} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }

  ${meiti_chaxun.pingban} {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }
`;

// 更新时间
const Gengxinshijian = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  
  ${meiti_chaxun.shouji} {
    font-size: 10px;
  }

  ${meiti_chaxun.pingban} {
    font-size: 10px;
  }
`;

// 空状态
const Kongzhuangtai = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
`;

// 更新栏组件
function Gengxinlan() {
  const [huoyuebiaoqian, shehuoyuebiaoqian] = useState('suoyou');

  // 模拟数据
  const gengxinshuju = [
    {
      id: 1,
      leixing: 'gonggao',
      biaoti: '查缺更新说明',
      shijian: '2025.04.14 08:31',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 2,
      leixing: 'jixie',
      biaoti: '【蒸汽朋友圈】经验值、持有率倍率行中！',
      shijian: '2024.05.27 03:45',
      biaoqian: ['suoyou', 'jixie']
    },
    {
      id: 3,
      leixing: 'gonggao',
      biaoti: '2025/08/05(二)维护预告',
      shijian: '2025.08.04 08:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 4,
      leixing: 'huodong',
      biaoti: '暑期打卡生物圈-深海',
      shijian: '2025.08.04 08:30',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 5,
      leixing: 'gonggao',
      biaoti: '2025/07/29(一)维护预告',
      shijian: '2025.07.29 06:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 6,
      leixing: 'gonggao',
      biaoti: '2025/07/29维护预告',
      shijian: '2025.07.28 08:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 7,
      leixing: 'jixie',
      biaoti: '2025/07/23(二)12:15临时维护预告',
      shijian: '2025.07.23 04:15',
      biaoqian: ['suoyou', 'jixie']
    },
    {
      id: 8,
      leixing: 'huodong',
      biaoti: '夏日祭典活动开启！限时装备获取',
      shijian: '2025.07.20 10:00',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 9,
      leixing: 'xinwen',
      biaoti: '新职业"符文骑士"即将登场',
      shijian: '2025.07.18 14:30',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 10,
      leixing: 'xitong',
      biaoti: '交易系统优化更新',
      shijian: '2025.07.15 09:15',
      biaoqian: ['suoyou', 'xitong']
    },
    {
      id: 11,
      leixing: 'huodong',
      biaoti: '公会战赛季开启通知',
      shijian: '2025.07.12 16:45',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 12,
      leixing: 'gonggao',
      biaoti: '服务器稳定性提升维护',
      shijian: '2025.07.10 08:00',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 13,
      leixing: 'xinwen',
      biaoti: '新地图"幻想森林"探索指南',
      shijian: '2025.07.08 11:20',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 14,
      leixing: 'jixie',
      biaoti: '自动战斗系统功能增强',
      shijian: '2025.07.05 13:30',
      biaoqian: ['suoyou', 'jixie']
    },
    {
      id: 15,
      leixing: 'huodong',
      biaoti: '端午节特别活动回顾',
      shijian: '2025.07.03 15:00',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 16,
      leixing: 'xitong',
      biaoti: '好友系统界面优化',
      shijian: '2025.07.01 10:45',
      biaoqian: ['suoyou', 'xitong']
    },
    {
      id: 17,
      leixing: 'gonggao',
      biaoti: '6月版本更新总结',
      shijian: '2025.06.30 17:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 18,
      leixing: 'xinwen',
      biaoti: '新宠物系统即将上线',
      shijian: '2025.06.28 12:15',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 19,
      leixing: 'huodong',
      biaoti: '周年庆典预热活动开始',
      shijian: '2025.06.25 14:00',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 20,
      leixing: 'jixie',
      biaoti: '技能快捷键设置优化',
      shijian: '2025.06.22 09:30',
      biaoqian: ['suoyou', 'jixie']
    },
    {
      id: 21,
      leixing: 'xitong',
      biaoti: '聊天系统表情包更新',
      shijian: '2025.06.20 16:20',
      biaoqian: ['suoyou', 'xitong']
    },
    {
      id: 22,
      leixing: 'gonggao',
      biaoti: '反外挂系统升级公告',
      shijian: '2025.06.18 08:45',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 23,
      leixing: 'xinwen',
      biaoti: '新副本"龙之巢穴"攻略',
      shijian: '2025.06.15 13:10',
      biaoqian: ['suoyou', 'xinwen']
    },
    {
      id: 24,
      leixing: 'huodong',
      biaoti: '限时商城特价活动',
      shijian: '2025.06.12 11:00',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 25,
      leixing: 'jixie',
      biaoti: '装备强化成功率调整',
      shijian: '2025.06.10 15:30',
      biaoqian: ['suoyou', 'jixie']
    }
  ];

  // 标签配置
  const biaoqianpeizhi = [
    { jian: 'suoyou', mingcheng: '所有' },
    { jian: 'huodong', mingcheng: '活动' },
    { jian: 'xinwen', mingcheng: '新闻' },
    { jian: 'xitong', mingcheng: '系统' },
    { jian: 'jixie', mingcheng: '机械' }
  ];

  // 图标映射
  const tubiaoying = {
    gonggao: '公',
    huodong: '活',
    xinwen: '新',
    xitong: '系',
    jixie: '机'
  };

  // 过滤数据
  const guolvshuju = gengxinshuju.filter(item => 
    huoyuebiaoqian === 'suoyou' || item.biaoqian.includes(huoyuebiaoqian)
  );

  return (
    <Gengxinlanrongqi>
      {/* 标签栏 */}
      <Biaoqianlanrongqi>
        {biaoqianpeizhi.map(biaoqian => (
          <Biaoqianxiang
            key={biaoqian.jian}
            huoyue={huoyuebiaoqian === biaoqian.jian}
            onClick={() => shehuoyuebiaoqian(biaoqian.jian)}
          >
            {biaoqian.mingcheng}
          </Biaoqianxiang>
        ))}
      </Biaoqianlanrongqi>

      {/* 内容区域 */}
      <Neirongquyu>
        <AnimatePresence mode="wait">
          {guolvshuju.length > 0 ? (
            <motion.div
              key={huoyuebiaoqian}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {guolvshuju.map(item => (
                <Gengxinxiang
                  key={item.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.05 }}
                >
                  <Gengxintubiao leixing={item.leixing}>
                    {tubiaoying[item.leixing]}
                  </Gengxintubiao>
                  <Gengxinneirong>
                    <Gengxinbiaoti>{item.biaoti}</Gengxinbiaoti>
                    <Gengxinshijian>{item.shijian}</Gengxinshijian>
                  </Gengxinneirong>
                </Gengxinxiang>
              ))}
            </motion.div>
          ) : (
            <Kongzhuangtai>
              暂无{biaoqianpeizhi.find(b => b.jian === huoyuebiaoqian)?.mingcheng}更新
            </Kongzhuangtai>
          )}
        </AnimatePresence>
      </Neirongquyu>
    </Gengxinlanrongqi>
  );
}

export default Gengxinlan;
